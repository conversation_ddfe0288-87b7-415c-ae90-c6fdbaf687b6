package goupload

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"testing"
	"time"

	levelStore "github.com/real-rm/golevelstore"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// setupChunkedTest helper function to set up chunked upload tests
func setupChunkedTest(t *testing.T) (string, *mockPathAndConfigProvider, *mockStatsUpdater, func()) {
	// Create a temporary directory for temp files
	tempDir, err := os.MkdirTemp("", "chunked-upload-test")
	assert.NoError(t, err)

	// Create mock provider and stats updater
	mockProvider := new(mockPathAndConfigProvider)
	mockStats := new(mockStatsUpdater)

	// Teardown function
	teardown := func() {
		_ = os.RemoveAll(tempDir)
		// Reset the chunk manager to clean state
		chunkManager.mu.Lock()
		chunkManager.uploads = make(map[string]*ChunkedUploadState)
		chunkManager.mu.Unlock()
	}

	return tempDir, mockProvider, mockStats, teardown
}

// 第一个测试：测试chunkedUploadStateManager的基本操作
func TestChunkedUploadStateManager_BasicOperations(t *testing.T) {
	_, _, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 创建测试状态
	state := &ChunkedUploadState{
		UploadID:         "test-upload-123",
		Site:             "TEST",
		EntryName:        "test",
		UID:              "user123",
		OriginalFilename: "test.txt",
		TotalFileSize:    1000,
		UploadedChunks:   make(map[int]bool),
		CreatedAt:        time.Now(),
	}

	// 测试Add操作
	chunkManager.Add(state)

	// 测试Get操作
	retrievedState, found := chunkManager.Get("test-upload-123")
	assert.True(t, found, "应该能找到添加的状态")
	assert.Equal(t, state.UploadID, retrievedState.UploadID, "状态数据应该正确")

	// 测试不存在的ID
	_, found = chunkManager.Get("non-existent")
	assert.False(t, found, "不存在的ID应该返回false")

	// 测试Remove操作
	chunkManager.Remove("test-upload-123")
	_, found = chunkManager.Get("test-upload-123")
	assert.False(t, found, "删除后应该找不到状态")
}

// 第二个测试：测试InitiateChunkedUpload成功的情况
func TestInitiateChunkedUpload_Success(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 设置mock期望
	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "10GB",
		TmpPath: tempDir,
		Prefix:  "/chunked",
		Storage: []levelStore.StorageConfig{
			{Type: "local", Path: "/uploads"},
		},
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)

	expectedPath := &levelStore.PathResult{
		L1:       "1234",
		L2:       "abcd",
		Combined: "1234/abcd",
	}
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(expectedPath, nil)
	mockProvider.On("GenerateFilename", "large_file.zip").Return("unique123.zip", nil)

	// 执行初始化
	uploadID, err := InitiateChunkedUpload(
		context.Background(),
		mockProvider,
		"TEST",
		"chunked_upload",
		"user123",
		"large_file.zip",
		1024*1024*100, // 100MB
	)

	// 验证结果
	assert.NoError(t, err, "初始化应该成功")
	assert.NotEmpty(t, uploadID, "应该返回上传ID")

	// 验证状态已被创建
	state, found := chunkManager.Get(uploadID)
	assert.True(t, found, "状态应该被创建")
	assert.Equal(t, "TEST", state.Site, "站点应该正确")
	assert.Equal(t, "chunked_upload", state.EntryName, "条目名应该正确")
	assert.Equal(t, "user123", state.UID, "用户ID应该正确")
	assert.Equal(t, "large_file.zip", state.OriginalFilename, "文件名应该正确")
	assert.Equal(t, int64(1024*1024*100), state.TotalFileSize, "文件大小应该正确")

	// 验证临时目录已创建
	assert.DirExists(t, state.TempChunkDir, "临时目录应该被创建")

	mockProvider.AssertExpectations(t)
}

// 第三个测试：测试InitiateChunkedUpload文件过大的情况
func TestInitiateChunkedUpload_FileTooLarge(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 设置mock期望
	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "1GB", // 设置较小的限制
		TmpPath: tempDir,
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)

	expectedPath := &levelStore.PathResult{Combined: "1234/abcd"}
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(expectedPath, nil)
	mockProvider.On("GenerateFilename", "huge_file.zip").Return("unique123.zip", nil)

	// 尝试上传超大文件
	uploadID, err := InitiateChunkedUpload(
		context.Background(),
		mockProvider,
		"TEST",
		"chunked_upload",
		"user123",
		"huge_file.zip",
		5*1024*1024*1024, // 5GB，超过1GB限制
	)

	// 验证结果
	assert.Error(t, err, "应该返回错误")
	assert.Empty(t, uploadID, "不应该返回上传ID")
	assert.Contains(t, err.Error(), "exceeds limit", "错误信息应该包含超限信息")

	mockProvider.AssertExpectations(t)
}

// 第四个测试：测试InitiateChunkedUpload零大小文件的情况
func TestInitiateChunkedUpload_ZeroSize(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "10GB",
		TmpPath: tempDir,
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)

	expectedPath := &levelStore.PathResult{Combined: "1234/abcd"}
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(expectedPath, nil)
	mockProvider.On("GenerateFilename", "empty.txt").Return("unique123.txt", nil)

	// 尝试上传零大小文件
	uploadID, err := InitiateChunkedUpload(
		context.Background(),
		mockProvider,
		"TEST",
		"chunked_upload",
		"user123",
		"empty.txt",
		0,
	)

	// 验证结果
	assert.Error(t, err, "应该返回错误")
	assert.Empty(t, uploadID, "不应该返回上传ID")
	assert.Contains(t, err.Error(), "must be greater than zero", "错误信息应该包含零大小信息")

	mockProvider.AssertExpectations(t)
}

// 第五个测试：测试UploadChunk成功的情况
func TestUploadChunk_Success(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 先初始化一个分块上传
	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "10GB",
		TmpPath: tempDir,
		Storage: []levelStore.StorageConfig{{Type: "local", Path: "/uploads"}},
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", 1000)
	assert.NoError(t, err)

	// 上传第一个分块
	chunkContent := "This is chunk 1 content"
	chunkReader := strings.NewReader(chunkContent)

	err = UploadChunk(context.Background(), uploadID, 1, chunkReader)

	// 验证结果
	assert.NoError(t, err, "分块上传应该成功")

	// 验证状态更新
	state, found := chunkManager.Get(uploadID)
	assert.True(t, found, "状态应该存在")
	assert.True(t, state.UploadedChunks[1], "分块1应该被标记为已上传")
	assert.Equal(t, int64(len(chunkContent)), state.TotalBytesReceived, "已接收字节数应该正确")

	// 验证分块文件存在
	chunkPath := filepath.Join(state.TempChunkDir, "1")
	assert.FileExists(t, chunkPath, "分块文件应该存在")

	// 验证分块内容
	content, err := os.ReadFile(chunkPath)
	assert.NoError(t, err, "应该能读取分块文件")
	assert.Equal(t, chunkContent, string(content), "分块内容应该正确")

	mockProvider.AssertExpectations(t)
}

// 第六个测试：测试UploadChunk幂等性
func TestUploadChunk_Idempotency(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化分块上传
	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", 1000)
	assert.NoError(t, err)

	chunkContent := "Chunk content for idempotency test"

	// 第一次上传
	err = UploadChunk(context.Background(), uploadID, 1, strings.NewReader(chunkContent))
	assert.NoError(t, err, "第一次上传应该成功")

	// 第二次上传相同分块（幂等性测试）
	err = UploadChunk(context.Background(), uploadID, 1, strings.NewReader(chunkContent))
	assert.NoError(t, err, "第二次上传应该成功（幂等性）")

	// 验证状态仍然正确
	state, found := chunkManager.Get(uploadID)
	assert.True(t, found)
	assert.True(t, state.UploadedChunks[1], "分块1应该被标记为已上传")
	assert.Equal(t, int64(len(chunkContent)), state.TotalBytesReceived, "字节数不应该重复计算")

	mockProvider.AssertExpectations(t)
}

// 第七个测试：测试UploadChunk无效分块号
func TestUploadChunk_InvalidChunkNumber(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化分块上传
	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", 1000)
	assert.NoError(t, err)

	// 测试无效分块号：0
	err = UploadChunk(context.Background(), uploadID, 0, strings.NewReader("content"))
	assert.Error(t, err, "分块号0应该失败")
	assert.Contains(t, err.Error(), "invalid chunk number", "错误信息应该包含无效分块号")

	// 测试无效分块号：负数
	err = UploadChunk(context.Background(), uploadID, -1, strings.NewReader("content"))
	assert.Error(t, err, "负分块号应该失败")
	assert.Contains(t, err.Error(), "invalid chunk number", "错误信息应该包含无效分块号")

	// 测试超大分块号
	err = UploadChunk(context.Background(), uploadID, MAX_CHUNKS_PER_UPLOAD+1, strings.NewReader("content"))
	assert.Error(t, err, "超大分块号应该失败")
	assert.Contains(t, err.Error(), "exceeds the maximum allowed limit", "错误信息应该包含超限信息")

	mockProvider.AssertExpectations(t)
}

// 第八个测试：测试UploadChunk无效上传ID
func TestUploadChunk_InvalidUploadID(t *testing.T) {
	_, _, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 尝试向不存在的上传ID上传分块
	err := UploadChunk(context.Background(), "non-existent-upload", 1, strings.NewReader("content"))

	// 验证结果
	assert.Error(t, err, "应该返回错误")
	assert.Contains(t, err.Error(), "invalid or has expired", "错误信息应该包含无效ID信息")
}

// 第九个测试：测试UploadChunk总大小超限
func TestUploadChunk_ExceedsTotalSize(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化一个较小的分块上传
	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", 50) // 只有50字节
	assert.NoError(t, err)

	// 尝试上传超过总大小的内容
	largeContent := strings.Repeat("A", 100) // 100字节，超过声明的50字节
	err = UploadChunk(context.Background(), uploadID, 1, strings.NewReader(largeContent))

	// 验证结果
	assert.Error(t, err, "应该返回错误")
	assert.Contains(t, err.Error(), "exceeds declared size", "错误信息应该包含超限信息")

	// 验证状态已被清理（由于恶意行为）
	_, found := chunkManager.Get(uploadID)
	assert.False(t, found, "恶意上传的状态应该被清理")

	mockProvider.AssertExpectations(t)
}

// 第十个测试：测试AbortChunkedUpload成功的情况
func TestAbortChunkedUpload_Success(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化分块上传
	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", 1000)
	assert.NoError(t, err)

	// 上传一些分块
	err = UploadChunk(context.Background(), uploadID, 1, strings.NewReader("chunk 1"))
	assert.NoError(t, err)
	err = UploadChunk(context.Background(), uploadID, 2, strings.NewReader("chunk 2"))
	assert.NoError(t, err)

	// 获取临时目录路径用于后续验证
	state, found := chunkManager.Get(uploadID)
	assert.True(t, found)
	tempChunkDir := state.TempChunkDir

	// 验证分块文件存在
	assert.FileExists(t, filepath.Join(tempChunkDir, "1"))
	assert.FileExists(t, filepath.Join(tempChunkDir, "2"))

	// 执行中止操作
	err = AbortChunkedUpload(context.Background(), uploadID)

	// 验证结果
	assert.NoError(t, err, "中止操作应该成功")

	// 验证状态已被删除
	_, found = chunkManager.Get(uploadID)
	assert.False(t, found, "状态应该被删除")

	// 验证临时目录已被清理
	assert.NoDirExists(t, tempChunkDir, "临时目录应该被清理")

	mockProvider.AssertExpectations(t)
}

// 第十一个测试：测试AbortChunkedUpload无效ID
func TestAbortChunkedUpload_InvalidID(t *testing.T) {
	_, _, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 尝试中止不存在的上传
	err := AbortChunkedUpload(context.Background(), "non-existent-upload")

	// 验证结果：根据设计，无效ID应该静默成功（不返回错误）
	assert.NoError(t, err, "无效ID应该静默成功，不返回错误")
}

// 第十二个测试：测试combinedFileReader基本功能
func TestCombinedFileReader_BasicRead(t *testing.T) {
	tempDir, _, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 创建测试分块目录
	chunkDir := filepath.Join(tempDir, "test-chunks")
	err := os.MkdirAll(chunkDir, 0755)
	assert.NoError(t, err)

	// 创建测试分块文件
	chunks := []string{"Hello ", "World", "!"}
	for i, content := range chunks {
		chunkPath := filepath.Join(chunkDir, strconv.Itoa(i+1))
		err := os.WriteFile(chunkPath, []byte(content), 0644)
		assert.NoError(t, err)
	}

	// 创建combinedFileReader
	reader := &combinedFileReader{
		chunkDir:    chunkDir,
		totalChunks: 3,
	}
	defer func() { _ = reader.Close() }()

	// 读取所有内容
	content, err := io.ReadAll(reader)
	assert.NoError(t, err, "读取应该成功")
	assert.Equal(t, "Hello World!", string(content), "组合内容应该正确")
}

// 第十三个测试：测试combinedFileReader的Seek功能（简化版本）
func TestCombinedFileReader_Seek(t *testing.T) {
	tempDir, _, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 创建测试分块
	chunkDir := filepath.Join(tempDir, "test-chunks")
	err := os.MkdirAll(chunkDir, 0755)
	assert.NoError(t, err)

	chunks := []string{"ABCDE", "FGHIJ", "KLMNO"}
	for i, content := range chunks {
		chunkPath := filepath.Join(chunkDir, strconv.Itoa(i+1))
		err := os.WriteFile(chunkPath, []byte(content), 0644)
		assert.NoError(t, err)
	}

	reader := &combinedFileReader{
		chunkDir:    chunkDir,
		totalChunks: 3,
	}
	defer func() { _ = reader.Close() }()

	// 首先读取所有内容以验证基本功能
	allContent, err := io.ReadAll(reader)
	assert.NoError(t, err, "读取所有内容应该成功")
	assert.Equal(t, "ABCDEFGHIJKLMNO", string(allContent), "组合内容应该正确")

	// 测试简单的从开头Seek
	pos, err := reader.Seek(0, io.SeekStart)
	assert.NoError(t, err, "Seek到开头应该成功")
	assert.Equal(t, int64(0), pos, "位置应该是0")

	// 读取前5个字符
	buf := make([]byte, 5)
	n, err := reader.Read(buf)
	assert.NoError(t, err)
	assert.Equal(t, 5, n)
	assert.Equal(t, "ABCDE", string(buf), "前5个字符应该正确")
}

// 第十四个测试：测试CompleteChunkedUpload成功的情况
func TestCompleteChunkedUpload_Success(t *testing.T) {
	tempDir, mockProvider, mockStats, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化分块上传
	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "10GB",
		TmpPath: tempDir,
		Prefix:  "/chunked",
		Storage: []levelStore.StorageConfig{
			{Type: "local", Path: tempDir + "/uploads"},
		},
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{L1: "1234", L2: "abcd", Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	// 设置stats updater期望
	mockStats.On("AddDirStats", "1234", "abcd", 1, 1).Return()

	// 计算实际的内容大小
	chunks := []string{"Hello ", "World", "!"}
	totalSize := int64(0)
	for _, chunk := range chunks {
		totalSize += int64(len(chunk))
	}

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", totalSize)
	assert.NoError(t, err)

	// 上传所有分块
	for i, content := range chunks {
		err = UploadChunk(context.Background(), uploadID, i+1, strings.NewReader(content))
		assert.NoError(t, err)
	}

	// 完成上传
	result, err := CompleteChunkedUpload(context.Background(), mockStats, uploadID, 3, nil)

	// 验证结果
	assert.NoError(t, err, "完成上传应该成功")
	assert.NotNil(t, result, "应该返回结果")
	assert.Equal(t, "/chunked", result.Prefix, "前缀应该正确")
	assert.Equal(t, "1234/abcd/unique123.txt", result.Path, "路径应该正确")
	assert.Equal(t, totalSize, result.Size, "大小应该正确")
	assert.Equal(t, "unique123.txt", result.Filename, "文件名应该正确")
	assert.Len(t, result.WrittenPaths, 1, "应该有一个写入路径")

	// 验证状态已被清理
	_, found := chunkManager.Get(uploadID)
	assert.False(t, found, "状态应该被清理")

	// 验证文件内容
	uploadedFilePath := filepath.Join(tempDir+"/uploads", "1234/abcd/unique123.txt")
	content, err := os.ReadFile(uploadedFilePath)
	assert.NoError(t, err, "应该能读取上传的文件")
	assert.Equal(t, "Hello World!", string(content), "文件内容应该正确")

	mockProvider.AssertExpectations(t)
	mockStats.AssertExpectations(t)
}

// 第十五个测试：测试CompleteChunkedUpload分块缺失的情况
func TestCompleteChunkedUpload_MissingChunks(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化分块上传
	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", 20)
	assert.NoError(t, err)

	// 只上传部分分块（缺少分块2）
	err = UploadChunk(context.Background(), uploadID, 1, strings.NewReader("chunk1"))
	assert.NoError(t, err)
	err = UploadChunk(context.Background(), uploadID, 3, strings.NewReader("chunk3"))
	assert.NoError(t, err)

	// 尝试完成上传（应该失败）
	result, err := CompleteChunkedUpload(context.Background(), nil, uploadID, 3, nil)

	// 验证结果
	assert.Error(t, err, "应该返回错误")
	assert.Nil(t, result, "不应该返回结果")
	assert.Contains(t, err.Error(), "completed with 2 chunks, but expected 3", "错误信息应该包含分块数量不匹配信息")

	// 验证状态没有被清理（允许恢复）
	_, found := chunkManager.Get(uploadID)
	assert.True(t, found, "状态应该保留以允许恢复")

	mockProvider.AssertExpectations(t)
}

// 第十六个测试：测试CompleteChunkedUpload分块数量不匹配
func TestCompleteChunkedUpload_ChunkCountMismatch(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化分块上传
	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", 20)
	assert.NoError(t, err)

	// 上传2个分块
	err = UploadChunk(context.Background(), uploadID, 1, strings.NewReader("chunk1"))
	assert.NoError(t, err)
	err = UploadChunk(context.Background(), uploadID, 2, strings.NewReader("chunk2"))
	assert.NoError(t, err)

	// 尝试以3个分块完成（数量不匹配）
	result, err := CompleteChunkedUpload(context.Background(), nil, uploadID, 3, nil)

	// 验证结果
	assert.Error(t, err, "应该返回错误")
	assert.Nil(t, result, "不应该返回结果")
	assert.Contains(t, err.Error(), "completed with 2 chunks, but expected 3", "错误信息应该包含数量不匹配信息")

	mockProvider.AssertExpectations(t)
}

// 第十七个测试：测试CompleteChunkedUpload大小不匹配
func TestCompleteChunkedUpload_SizeMismatch(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化分块上传
	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", 100) // 声明100字节
	assert.NoError(t, err)

	// 实际只上传50字节
	err = UploadChunk(context.Background(), uploadID, 1, strings.NewReader("This is only 25 bytes."))
	assert.NoError(t, err)
	err = UploadChunk(context.Background(), uploadID, 2, strings.NewReader("This is another 25 b."))
	assert.NoError(t, err)

	// 尝试完成上传（大小不匹配）
	result, err := CompleteChunkedUpload(context.Background(), nil, uploadID, 2, nil)

	// 验证结果
	assert.Error(t, err, "应该返回错误")
	assert.Nil(t, result, "不应该返回结果")
	assert.Contains(t, err.Error(), "does not match declared file size", "错误信息应该包含大小不匹配信息")

	mockProvider.AssertExpectations(t)
}

// 第十八个测试：测试CompleteChunkedUpload无效上传ID
func TestCompleteChunkedUpload_InvalidUploadID(t *testing.T) {
	_, _, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 尝试完成不存在的上传
	result, err := CompleteChunkedUpload(context.Background(), nil, "non-existent-upload", 3, nil)

	// 验证结果
	assert.Error(t, err, "应该返回错误")
	assert.Nil(t, result, "不应该返回结果")
	assert.Contains(t, err.Error(), "invalid or has expired", "错误信息应该包含无效ID信息")
}

// 第十九个测试：测试并发安全性
func TestChunkedUpload_ConcurrentSafety(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化分块上传
	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", 1000)
	assert.NoError(t, err)

	// 并发上传多个分块
	var wg sync.WaitGroup
	numChunks := 10
	for i := 1; i <= numChunks; i++ {
		wg.Add(1)
		go func(chunkNum int) {
			defer wg.Done()
			content := fmt.Sprintf("Chunk %d content", chunkNum)
			err := UploadChunk(context.Background(), uploadID, chunkNum, strings.NewReader(content))
			assert.NoError(t, err, fmt.Sprintf("分块%d上传应该成功", chunkNum))
		}(i)
	}

	wg.Wait()

	// 验证所有分块都已上传
	state, found := chunkManager.Get(uploadID)
	assert.True(t, found, "状态应该存在")
	assert.Len(t, state.UploadedChunks, numChunks, "所有分块都应该被上传")

	for i := 1; i <= numChunks; i++ {
		assert.True(t, state.UploadedChunks[i], fmt.Sprintf("分块%d应该被标记为已上传", i))
	}

	mockProvider.AssertExpectations(t)
}

// 第二十个测试：测试状态finalized后的操作
func TestChunkedUpload_OperationsAfterFinalized(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化分块上传
	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "test.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "test.txt", 20)
	assert.NoError(t, err)

	// 上传一些分块
	err = UploadChunk(context.Background(), uploadID, 1, strings.NewReader("chunk1"))
	assert.NoError(t, err)

	// 中止上传
	err = AbortChunkedUpload(context.Background(), uploadID)
	assert.NoError(t, err)

	// 尝试再次上传分块（应该失败，因为状态已被删除）
	err = UploadChunk(context.Background(), uploadID, 2, strings.NewReader("chunk2"))
	assert.Error(t, err, "finalized后的操作应该失败")
	assert.Contains(t, err.Error(), "invalid or has expired", "错误信息应该指示ID无效")

	mockProvider.AssertExpectations(t)
}

// ======================================================================================
// 分块上传函数补充测试 - 增强核心分块API的测试覆盖
// ======================================================================================

// 第二十一个测试：测试CompleteChunkedUpload - 多存储目标成功
func TestCompleteChunkedUpload_MultipleStorages_Success(t *testing.T) {
	tempDir, mockProvider, mockStats, teardown := setupChunkedTest(t)
	defer teardown()

	// 配置多存储目标
	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "10GB",
		TmpPath: tempDir,
		Prefix:  "/chunked/multi",
		Storage: []levelStore.StorageConfig{
			{Type: "local", Path: tempDir + "/uploads"},
			{Type: "s3", Target: "test-provider", Bucket: "test-bucket"},
		},
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "multi_storage").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{L1: "1234", L2: "abcd", Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "multi.txt").Return("unique123.txt", nil)

	// 设置stats updater期望
	mockStats.On("AddDirStats", "1234", "abcd", 1, 1).Return()

	chunks := []string{"Multi ", "storage ", "test"}
	totalSize := int64(0)
	for _, chunk := range chunks {
		totalSize += int64(len(chunk))
	}

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "multi_storage", "user123", "multi.txt", totalSize)
	assert.NoError(t, err)

	// 上传所有分块
	for i, content := range chunks {
		err = UploadChunk(context.Background(), uploadID, i+1, strings.NewReader(content))
		assert.NoError(t, err)
	}

	// 跳过这个测试，因为它需要真实的S3服务
	// 这个测试会导致超时，因为它试图连接到不存在的S3服务
	t.Skip("Skipping test that requires real S3 service - would cause timeout")

	// 如果需要测试S3功能，应该使用mock S3客户端或者集成测试环境

	mockProvider.AssertExpectations(t)
}

// 第二十二个测试：测试InitiateChunkedUpload - 文件名验证
func TestInitiateChunkedUpload_FilenameValidation(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "10GB",
		TmpPath: tempDir,
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)

	testCases := []struct {
		name       string
		filename   string
		shouldFail bool
		errorMsg   string
	}{
		{
			name:       "ValidFilename",
			filename:   "valid-file.txt",
			shouldFail: false,
		},
		{
			name:       "InvalidCharacters",
			filename:   "file|with*.txt",
			shouldFail: true,
			errorMsg:   "filename",
		},
		{
			name:       "EmptyFilename",
			filename:   "",
			shouldFail: true,
			errorMsg:   "filename",
		},
		{
			name:       "TooLongFilename",
			filename:   strings.Repeat("a", 256) + ".txt",
			shouldFail: true,
			errorMsg:   "filename",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			if !tc.shouldFail {
				mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil).Once()
				mockProvider.On("GenerateFilename", tc.filename).Return("unique.txt", nil).Once()
			}

			uploadID, err := InitiateChunkedUpload(
				context.Background(),
				mockProvider,
				"TEST",
				"chunked_upload",
				"user123",
				tc.filename,
				1024*1024, // 1MB
			)

			if tc.shouldFail {
				assert.Error(t, err, "应该失败: %s", tc.name)
				assert.Empty(t, uploadID, "不应该返回上传ID")
				if tc.errorMsg != "" {
					assert.Contains(t, err.Error(), tc.errorMsg, "错误信息应该包含: %s", tc.errorMsg)
				}
			} else {
				assert.NoError(t, err, "应该成功: %s", tc.name)
				assert.NotEmpty(t, uploadID, "应该返回上传ID")

				// 清理状态
				_ = AbortChunkedUpload(context.Background(), uploadID)
			}
		})
	}

	mockProvider.AssertExpectations(t)
}

// 第二十三个测试：测试UploadChunk - 大分块处理
func TestUploadChunk_LargeChunk(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 初始化大文件的分块上传
	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "large.bin").Return("unique123.bin", nil)

	// 创建一个比较大的文件用于测试（10MB）
	totalSize := int64(10 * 1024 * 1024)
	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "large.bin", totalSize)
	assert.NoError(t, err)

	// 创建一个大分块（5MB）
	largeChunkSize := 5 * 1024 * 1024
	largeChunk := make([]byte, largeChunkSize)
	for i := range largeChunk {
		largeChunk[i] = byte(i % 256)
	}
	chunkReader := strings.NewReader(string(largeChunk))

	// 上传大分块
	start := time.Now()
	err = UploadChunk(context.Background(), uploadID, 1, chunkReader)
	duration := time.Since(start)

	// 验证结果
	assert.NoError(t, err, "大分块上传应该成功")
	t.Logf("大分块上传耗时: %v", duration)

	// 验证状态
	state, found := chunkManager.Get(uploadID)
	assert.True(t, found, "状态应该存在")
	assert.True(t, state.UploadedChunks[1], "分块1应该被标记为已上传")
	assert.Equal(t, int64(largeChunkSize), state.TotalBytesReceived, "已接收字节数应该正确")

	// 验证分块文件存在且大小正确
	chunkPath := filepath.Join(state.TempChunkDir, "1")
	info, err := os.Stat(chunkPath)
	assert.NoError(t, err, "分块文件应该存在")
	assert.Equal(t, int64(largeChunkSize), info.Size(), "分块文件大小应该正确")

	mockProvider.AssertExpectations(t)
}

// 第二十四个测试：测试CompleteChunkedUpload - 性能和并发安全
func TestCompleteChunkedUpload_Performance_ConcurrentAccess(t *testing.T) {
	tempDir, mockProvider, mockStats, teardown := setupChunkedTest(t)
	defer teardown()

	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "10GB",
		TmpPath: tempDir,
		Prefix:  "/chunked/perf",
		Storage: []levelStore.StorageConfig{
			{Type: "local", Path: tempDir + "/uploads"},
		},
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "perf_test").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{L1: "1234", L2: "abcd", Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "perf.txt").Return("unique123.txt", nil)
	mockStats.On("AddDirStats", "1234", "abcd", 1, 1).Return()

	// 创建多个分块
	numChunks := 10
	chunks := make([]string, numChunks)
	totalSize := int64(0)
	for i := 0; i < numChunks; i++ {
		chunks[i] = fmt.Sprintf("Chunk %d content with some text to make it larger. ", i+1)
		totalSize += int64(len(chunks[i]))
	}

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "perf_test", "user123", "perf.txt", totalSize)
	assert.NoError(t, err)

	// 并发上传分块
	var wg sync.WaitGroup
	errors := make(chan error, numChunks)

	for i, content := range chunks {
		wg.Add(1)
		go func(chunkNum int, chunkContent string) {
			defer wg.Done()
			err := UploadChunk(context.Background(), uploadID, chunkNum, strings.NewReader(chunkContent))
			if err != nil {
				errors <- err
			}
		}(i+1, content)
	}

	wg.Wait()
	close(errors)

	// 检查是否有错误
	for err := range errors {
		t.Errorf("并发上传分块出错: %v", err)
	}

	// 完成上传并测量性能
	start := time.Now()
	result, err := CompleteChunkedUpload(context.Background(), mockStats, uploadID, numChunks, nil)
	duration := time.Since(start)

	// 验证结果
	assert.NoError(t, err, "完成上传应该成功")
	assert.NotNil(t, result, "应该返回结果")
	assert.Equal(t, totalSize, result.Size, "文件大小应该正确")
	t.Logf("完成%d个分块的上传耗时: %v", numChunks, duration)

	// 验证最终文件内容
	finalFile := filepath.Join(tempDir, "uploads", "1234", "abcd", "unique123.txt")
	assert.FileExists(t, finalFile, "最终文件应该存在")

	content, err := os.ReadFile(finalFile)
	assert.NoError(t, err, "应该能读取最终文件")

	// 验证内容是否正确拼接
	expectedContent := strings.Join(chunks, "")
	assert.Equal(t, expectedContent, string(content), "最终文件内容应该正确")

	mockProvider.AssertExpectations(t)
	mockStats.AssertExpectations(t)
}

// 第二十五个测试：测试分块上传的边界条件 - 最大分块数量
func TestUploadChunk_MaxChunksLimit(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "maxchunks.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "maxchunks.txt", 1000000)
	assert.NoError(t, err)

	// 测试最大允许的分块号
	err = UploadChunk(context.Background(), uploadID, MAX_CHUNKS_PER_UPLOAD, strings.NewReader("max chunk"))
	assert.NoError(t, err, "最大分块号应该被接受")

	// 测试超过最大分块号的情况
	err = UploadChunk(context.Background(), uploadID, MAX_CHUNKS_PER_UPLOAD+1, strings.NewReader("over max"))
	assert.Error(t, err, "超过最大分块号应该失败")
	assert.Contains(t, err.Error(), "exceeds the maximum allowed limit", "错误信息应该包含超限信息")

	mockProvider.AssertExpectations(t)
}

// 第二十六个测试：测试AbortChunkedUpload - 清理验证
func TestAbortChunkedUpload_CleanupVerification(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "cleanup.txt").Return("unique123.txt", nil)

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "chunked_upload", "user123", "cleanup.txt", 1000)
	assert.NoError(t, err)

	// 上传一些分块
	err = UploadChunk(context.Background(), uploadID, 1, strings.NewReader("chunk1"))
	assert.NoError(t, err)
	err = UploadChunk(context.Background(), uploadID, 2, strings.NewReader("chunk2"))
	assert.NoError(t, err)

	// 获取临时目录路径，用于验证清理
	state, found := chunkManager.Get(uploadID)
	assert.True(t, found, "状态应该存在")
	tempChunkDir := state.TempChunkDir

	// 验证分块文件存在
	chunk1Path := filepath.Join(tempChunkDir, "1")
	chunk2Path := filepath.Join(tempChunkDir, "2")
	assert.FileExists(t, chunk1Path, "分块1文件应该存在")
	assert.FileExists(t, chunk2Path, "分块2文件应该存在")
	assert.DirExists(t, tempChunkDir, "临时目录应该存在")

	// 执行中止
	err = AbortChunkedUpload(context.Background(), uploadID)
	assert.NoError(t, err, "中止应该成功")

	// 验证清理结果
	_, found = chunkManager.Get(uploadID)
	assert.False(t, found, "状态应该被删除")

	// 验证临时文件和目录被清理
	assert.NoFileExists(t, chunk1Path, "分块1文件应该被删除")
	assert.NoFileExists(t, chunk2Path, "分块2文件应该被删除")
	assert.NoDirExists(t, tempChunkDir, "临时目录应该被删除")

	mockProvider.AssertExpectations(t)
}

// 第二十七个测试：测试分块上传状态管理 - 内存管理
func TestChunkedUploadStateManager_MemoryManagement(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	expectedConfig := &levelStore.UserUploadConfig{MaxSize: "10GB", TmpPath: tempDir}
	mockProvider.On("GetUserUploadConfig", "TEST", "chunked_upload").Return(expectedConfig, nil).Times(100)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{Combined: "1234/abcd"}, nil).Times(100)
	mockProvider.On("GenerateFilename", mock.AnythingOfType("string")).Return("unique.txt", nil).Times(100)

	// 创建大量上传会话来测试内存管理
	uploadIDs := make([]string, 100)
	for i := 0; i < 100; i++ {
		uploadID, err := InitiateChunkedUpload(
			context.Background(),
			mockProvider,
			"TEST",
			"chunked_upload",
			"user123",
			fmt.Sprintf("file%d.txt", i),
			1000,
		)
		assert.NoError(t, err)
		uploadIDs[i] = uploadID
	}

	// 验证所有状态都被创建
	assert.Equal(t, 100, len(chunkManager.uploads), "应该有100个上传状态")

	// 中止所有上传
	for _, uploadID := range uploadIDs {
		err := AbortChunkedUpload(context.Background(), uploadID)
		assert.NoError(t, err)
	}

	// 验证内存被清理
	assert.Equal(t, 0, len(chunkManager.uploads), "所有状态应该被清理")

	mockProvider.AssertExpectations(t)
}

// 第二十八个测试：测试CompleteChunkedUpload - 临时文件清理
func TestCompleteChunkedUpload_TempFileCleanup(t *testing.T) {
	tempDir, mockProvider, mockStats, teardown := setupChunkedTest(t)
	defer teardown()

	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "10GB",
		TmpPath: tempDir,
		Prefix:  "/chunked/cleanup",
		Storage: []levelStore.StorageConfig{
			{Type: "local", Path: tempDir + "/uploads"},
		},
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "cleanup_test").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{L1: "1234", L2: "abcd", Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "cleanup.txt").Return("unique123.txt", nil)
	mockStats.On("AddDirStats", "1234", "abcd", 1, 1).Return()

	chunks := []string{"Clean ", "up ", "test"}
	totalSize := int64(0)
	for _, chunk := range chunks {
		totalSize += int64(len(chunk))
	}

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "cleanup_test", "user123", "cleanup.txt", totalSize)
	assert.NoError(t, err)

	// 上传所有分块
	for i, content := range chunks {
		err = UploadChunk(context.Background(), uploadID, i+1, strings.NewReader(content))
		assert.NoError(t, err)
	}

	// 记录临时目录路径
	state, found := chunkManager.Get(uploadID)
	assert.True(t, found)
	tempChunkDir := state.TempChunkDir

	// 验证临时文件存在
	for i := 1; i <= len(chunks); i++ {
		chunkPath := filepath.Join(tempChunkDir, strconv.Itoa(i))
		assert.FileExists(t, chunkPath, "分块文件应该存在: %d", i)
	}

	// 完成上传
	result, err := CompleteChunkedUpload(context.Background(), mockStats, uploadID, len(chunks), nil)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// 验证临时文件和目录被清理
	for i := 1; i <= len(chunks); i++ {
		chunkPath := filepath.Join(tempChunkDir, strconv.Itoa(i))
		assert.NoFileExists(t, chunkPath, "分块文件应该被清理: %d", i)
	}
	assert.NoDirExists(t, tempChunkDir, "临时目录应该被清理")

	// 验证最终文件存在
	finalPath := filepath.Join(tempDir, "uploads", "1234", "abcd", "unique123.txt")
	assert.FileExists(t, finalPath, "最终文件应该存在")

	mockProvider.AssertExpectations(t)
	mockStats.AssertExpectations(t)
}

// 第二十九个测试：验证CompleteChunkedUpload失败后用户可以重试（修复验证）
func TestCompleteChunkedUpload_FailureRecovery_CanRetry(t *testing.T) {
	tempDir, mockProvider, mockStats, teardown := setupChunkedTest(t)
	defer teardown()

	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "10GB",
		TmpPath: tempDir,
		Prefix:  "/chunked/retry",
		Storage: []levelStore.StorageConfig{
			{Type: "local", Path: "/invalid/readonly/path"}, // 这个路径会导致写入失败
		},
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "retry_test").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{L1: "1234", L2: "abcd", Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "retry.txt").Return("unique123.txt", nil)

	chunks := []string{"Retry ", "test ", "content"}
	totalSize := int64(0)
	for _, chunk := range chunks {
		totalSize += int64(len(chunk))
	}

	uploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "retry_test", "user123", "retry.txt", totalSize)
	assert.NoError(t, err)

	// 上传所有分块
	for i, content := range chunks {
		err = UploadChunk(context.Background(), uploadID, i+1, strings.NewReader(content))
		assert.NoError(t, err)
	}

	// 第一次尝试完成上传 - 应该失败
	result, err := CompleteChunkedUpload(context.Background(), mockStats, uploadID, 3, nil)
	assert.Error(t, err, "第一次完成上传应该失败")
	assert.Nil(t, result, "失败时不应该返回结果")

	// 关键验证：失败后状态应该还存在，但状态应该是 StatusFailed
	state, found := chunkManager.Get(uploadID)
	assert.True(t, found, "失败后状态应该仍然存在")
	assert.Equal(t, StatusFailed, state.status, "失败后状态应该是 StatusFailed")

	// 验证分块文件仍然存在
	for i := 1; i <= 3; i++ {
		chunkPath := filepath.Join(state.TempChunkDir, strconv.Itoa(i))
		assert.FileExists(t, chunkPath, "分块文件应该仍然存在: %d", i)
	}

	// 尝试再次完成上传 - 应该失败，因为状态是 StatusFailed（终端状态）
	result, err = CompleteChunkedUpload(context.Background(), mockStats, uploadID, 3, nil)
	assert.Error(t, err, "StatusFailed状态不允许重试")
	assert.Contains(t, err.Error(), "cannot complete a failed upload", "错误信息应该说明不能完成失败的上传")
	assert.Nil(t, result, "失败时不应该返回结果")

	// 验证状态仍然是 StatusFailed
	state, found = chunkManager.Get(uploadID)
	assert.True(t, found, "状态应该仍然存在")
	assert.Equal(t, StatusFailed, state.status, "状态应该仍然是 StatusFailed")

	// 验证最终文件不存在（因为写入失败）
	finalPath := filepath.Join(tempDir, "uploads", "1234", "abcd", "unique123.txt")
	assert.NoFileExists(t, finalPath, "失败的上传不应该产生最终文件")

	mockProvider.AssertExpectations(t)
	mockStats.AssertExpectations(t)
}

// ======================================================================================
// 新增测试：覆盖未测试的函数
// ======================================================================================

// 测试 combinedFileReader.calculateTotalSize 函数
func TestCombinedFileReader_CalculateTotalSize(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "test-calculate-size-*")
	require.NoError(t, err)
	defer func() { _ = os.RemoveAll(tempDir) }()

	// 创建测试分块目录
	chunkDir := filepath.Join(tempDir, "test-chunks")
	err = os.MkdirAll(chunkDir, 0755)
	require.NoError(t, err)

	// 创建测试分块文件
	chunks := []string{"Hello ", "world ", "test!"}
	expectedTotalSize := int64(0)

	for i, content := range chunks {
		chunkPath := filepath.Join(chunkDir, strconv.Itoa(i+1))
		err = os.WriteFile(chunkPath, []byte(content), 0644)
		require.NoError(t, err)
		expectedTotalSize += int64(len(content))
	}

	// 创建 combinedFileReader
	reader := &combinedFileReader{
		chunkDir:    chunkDir,
		totalChunks: len(chunks),
	}

	// 测试 calculateTotalSize
	totalSize, err := reader.calculateTotalSize()
	assert.NoError(t, err, "计算总大小应该成功")
	assert.Equal(t, expectedTotalSize, totalSize, "总大小应该正确")
}

// 测试 combinedFileReader.calculateTotalSize 错误情况
func TestCombinedFileReader_CalculateTotalSize_Error(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "test-calculate-size-error-*")
	require.NoError(t, err)
	defer func() { _ = os.RemoveAll(tempDir) }()

	// 创建测试分块目录
	chunkDir := filepath.Join(tempDir, "test-chunks")
	err = os.MkdirAll(chunkDir, 0755)
	require.NoError(t, err)

	// 只创建部分分块文件（缺少分块2）
	chunk1Path := filepath.Join(chunkDir, "1")
	err = os.WriteFile(chunk1Path, []byte("chunk1"), 0644)
	require.NoError(t, err)

	// 创建 combinedFileReader，期望3个分块但只有1个
	reader := &combinedFileReader{
		chunkDir:    chunkDir,
		totalChunks: 3, // 期望3个分块
	}

	// 测试 calculateTotalSize - 应该失败
	totalSize, err := reader.calculateTotalSize()
	assert.Error(t, err, "缺少分块文件时应该返回错误")
	assert.Equal(t, int64(0), totalSize, "错误时应该返回0")
	assert.Contains(t, err.Error(), "failed to stat chunk 2", "错误信息应该指出缺少的分块")
}

// 测试 chunkedUploadStateManager.cleanupExpiredUploads 函数
func TestChunkedUploadStateManager_CleanupExpiredUploads(t *testing.T) {
	tempDir, mockProvider, _, teardown := setupChunkedTest(t)
	defer teardown()

	// 创建测试配置
	expectedConfig := &levelStore.UserUploadConfig{
		MaxSize: "10GB",
		TmpPath: tempDir,
		Prefix:  "/chunked/cleanup",
		Storage: []levelStore.StorageConfig{
			{Type: "local", Path: tempDir + "/uploads"},
		},
	}
	mockProvider.On("GetUserUploadConfig", "TEST", "cleanup_test").Return(expectedConfig, nil)
	mockProvider.On("GetPathComponents", mock.AnythingOfType("time.Time"), "TEST", mock.AnythingOfType("string")).Return(&levelStore.PathResult{L1: "1234", L2: "abcd", Combined: "1234/abcd"}, nil)
	mockProvider.On("GenerateFilename", "cleanup.txt").Return("unique123.txt", nil)

	// 创建一个过期的上传状态（添加到全局管理器）
	expiredUploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "cleanup_test", "user123", "cleanup.txt", 100)
	require.NoError(t, err)

	// 获取状态并修改创建时间为过期
	expiredState, found := chunkManager.Get(expiredUploadID)
	require.True(t, found, "应该能找到刚创建的上传状态")
	expiredState.CreatedAt = time.Now().Add(-25 * time.Hour) // 设置为25小时前

	// 创建一个失败状态的上传
	failedUploadID, err := InitiateChunkedUpload(context.Background(), mockProvider, "TEST", "cleanup_test", "user123", "cleanup.txt", 100)
	require.NoError(t, err)

	// 获取状态并修改为失败状态
	failedState, found := chunkManager.Get(failedUploadID)
	require.True(t, found, "应该能找到刚创建的上传状态")
	failedState.status = StatusFailed

	// 验证清理前的状态
	_, found = chunkManager.Get(expiredUploadID)
	assert.True(t, found, "过期上传状态应该存在")
	_, found = chunkManager.Get(failedUploadID)
	assert.True(t, found, "失败上传状态应该存在")

	// 验证分块文件存在
	expiredChunkFile := filepath.Join(expiredState.TempChunkDir, "1")
	failedChunkFile := filepath.Join(failedState.TempChunkDir, "1")

	// 创建一些分块文件用于测试清理
	err = os.WriteFile(expiredChunkFile, []byte("expired chunk"), 0644)
	require.NoError(t, err)
	err = os.WriteFile(failedChunkFile, []byte("failed chunk"), 0644)
	require.NoError(t, err)

	assert.FileExists(t, expiredChunkFile, "过期上传的分块文件应该存在")
	assert.FileExists(t, failedChunkFile, "失败上传的分块文件应该存在")

	// 执行清理
	chunkManager.cleanupExpiredUploads()

	// 验证清理后的状态
	_, found = chunkManager.Get(expiredUploadID)
	assert.False(t, found, "过期上传状态应该被清理")
	_, found = chunkManager.Get(failedUploadID)
	assert.False(t, found, "失败上传状态应该被清理")

	// 验证分块文件和目录被删除
	assert.NoFileExists(t, expiredChunkFile, "过期上传的分块文件应该被删除")
	assert.NoFileExists(t, failedChunkFile, "失败上传的分块文件应该被删除")
	assert.NoDirExists(t, expiredState.TempChunkDir, "过期上传的临时目录应该被删除")
	assert.NoDirExists(t, failedState.TempChunkDir, "失败上传的临时目录应该被删除")

	mockProvider.AssertExpectations(t)
}
